{"name": "the-ice-box", "private": true, "version": "1.0.0", "description": "The Ice Box - Premier Hockey Equipment", "type": "module", "engines": {"node": ">=18.0.0", "npm": ">=8.0.0"}, "keywords": ["hockey", "equipment", "sports", "react", "vite"], "author": "<PERSON>", "license": "MIT", "dependencies": {"@googlemaps/google-maps-services-js": "^3.4.2", "@hono/zod-validator": "^0.5.0", "dotenv": "^17.2.1", "hono": "4.7.7", "lucide-react": "^0.510.0", "react": "19.0.0", "react-dom": "19.0.0", "react-router": "^7.5.3", "zod": "^3.24.3"}, "devDependencies": {"@eslint/js": "9.25.1", "@types/node": "22.14.1", "@types/react": "19.0.10", "@types/react-dom": "19.0.4", "@vitejs/plugin-react": "4.4.1", "autoprefixer": "^10.4.21", "eslint": "9.25.1", "eslint-plugin-react-hooks": "5.2.0", "eslint-plugin-react-refresh": "0.4.19", "globals": "15.15.0", "postcss": "^8.5.3", "tailwindcss": "^3.4.17", "typescript": "5.8.3", "typescript-eslint": "8.31.0", "vite": "6.3.2"}, "scripts": {"build": "tsc -b && vite build", "build:prod": "NODE_ENV=production npm run build", "build:netlify": "tsc -p tsconfig.app.json && vite build", "preview": "vite preview", "dev": "vite", "lint": "eslint .", "lint:fix": "eslint . --fix", "type-check": "tsc --noEmit", "clean": "node -e \"const fs = require('fs'); ['dist', 'build', '.netlify'].forEach(dir => { try { fs.rmSync(dir, { recursive: true, force: true }); } catch (e) { /* ignore */ } })\"", "prebuild": "npm run clean && npm run type-check", "postbuild": "echo 'Build completed successfully'", "netlify:build": "npm run build:prod", "netlify:dev": "netlify dev"}}
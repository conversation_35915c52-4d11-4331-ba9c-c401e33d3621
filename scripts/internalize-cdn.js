#!/usr/bin/env node

/**
 * CDN Internalization Script
 * 
 * This script automatically:
 * 1. Scans the entire codebase for CDN URLs
 * 2. Downloads all external assets
 * 3. Organizes them in a logical directory structure
 * 4. Updates all references to point to local files
 * 5. Creates a mapping log for reference
 * 6. Verifies file integrity with checksums
 */

import fs from 'fs/promises';
import path from 'path';
import https from 'https';
import http from 'http';
import crypto from 'crypto';
import { URL } from 'url';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

// Get current directory for ES modules
const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

// Configuration
const CONFIG = {
  // Directory to store downloaded assets
  assetsDir: 'src/assets',
  // Supported file extensions to scan
  scanExtensions: ['.html', '.css', '.js', '.jsx', '.ts', '.tsx', '.json', '.md'],
  // CDN URL patterns to match
  cdnPatterns: [
    /https?:\/\/[^\s"'<>]+\.(webp|jpg|jpeg|png|gif|svg|css|js|woff|woff2|ttf|ico|pdf|mp4|webm)/gi,
    /https?:\/\/mocha-cdn\.com\/[^\s"'<>]+/gi,
    /https?:\/\/cdn\.[^\s"'<>]+/gi,
    /https?:\/\/[^\s"'<>]*cdn[^\s"'<>]+/gi
  ],
  // File type organization
  fileTypeMapping: {
    images: ['.webp', '.jpg', '.jpeg', '.png', '.gif', '.svg', '.ico'],
    styles: ['.css'],
    scripts: ['.js'],
    fonts: ['.woff', '.woff2', '.ttf', '.otf'],
    documents: ['.pdf'],
    videos: ['.mp4', '.webm', '.avi', '.mov']
  },
  // Maximum concurrent downloads
  maxConcurrentDownloads: 5,
  // Timeout for downloads (ms)
  downloadTimeout: 30000
};

class CDNInternalizer {
  constructor() {
    this.foundUrls = new Set();
    this.downloadedFiles = new Map(); // URL -> local path mapping
    this.failedDownloads = new Set();
    this.checksums = new Map();
    this.projectRoot = process.cwd();
    this.logFile = path.join(this.projectRoot, 'cdn-internalization.log');
  }

  /**
   * Main execution function
   */
  async run() {
    try {
      console.log('🚀 Starting CDN internalization process...');
      
      // Step 1: Scan for CDN URLs
      await this.scanForCDNUrls();
      console.log(`📊 Found ${this.foundUrls.size} unique CDN URLs`);
      
      if (this.foundUrls.size === 0) {
        console.log('✅ No CDN URLs found. Nothing to internalize.');
        return;
      }
      
      // Step 2: Create asset directory structure
      await this.createAssetStructure();
      
      // Step 3: Download all files
      await this.downloadAllFiles();
      
      // Step 4: Update all references
      await this.updateAllReferences();
      
      // Step 5: Generate mapping log
      await this.generateMappingLog();
      
      // Step 6: Generate summary report
      this.generateSummaryReport();
      
      console.log('✅ CDN internalization completed successfully!');
      
    } catch (error) {
      console.error('❌ Error during CDN internalization:', error);
      process.exit(1);
    }
  }

  /**
   * Recursively scan all files for CDN URLs
   */
  async scanForCDNUrls() {
    console.log('🔍 Scanning codebase for CDN URLs...');
    
    const scanDir = async (dirPath) => {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        // Skip node_modules, .git, and other common directories
        if (entry.isDirectory()) {
          if (!['node_modules', '.git', 'dist', 'build', '.next'].includes(entry.name)) {
            await scanDir(fullPath);
          }
        } else if (entry.isFile()) {
          const ext = path.extname(entry.name).toLowerCase();
          if (CONFIG.scanExtensions.includes(ext)) {
            await this.scanFile(fullPath);
          }
        }
      }
    };
    
    await scanDir(this.projectRoot);
  }

  /**
   * Scan individual file for CDN URLs
   */
  async scanFile(filePath) {
    try {
      const content = await fs.readFile(filePath, 'utf8');
      
      for (const pattern of CONFIG.cdnPatterns) {
        const matches = content.match(pattern);
        if (matches) {
          matches.forEach(url => {
            // Clean up the URL (remove quotes, trailing characters)
            const cleanUrl = url.replace(/["'`]/g, '').replace(/[,;)\]}>\s]*$/, '');
            if (this.isValidUrl(cleanUrl)) {
              this.foundUrls.add(cleanUrl);
            }
          });
        }
      }
    } catch (error) {
      console.warn(`⚠️  Could not scan file ${filePath}:`, error.message);
    }
  }

  /**
   * Validate if string is a valid URL
   */
  isValidUrl(string) {
    try {
      new URL(string);
      return true;
    } catch {
      return false;
    }
  }

  /**
   * Create organized directory structure for assets
   */
  async createAssetStructure() {
    console.log('📁 Creating asset directory structure...');
    
    const assetsPath = path.join(this.projectRoot, CONFIG.assetsDir);
    
    // Create main assets directory
    await fs.mkdir(assetsPath, { recursive: true });
    
    // Create subdirectories for each file type
    for (const [category] of Object.entries(CONFIG.fileTypeMapping)) {
      const categoryPath = path.join(assetsPath, category);
      await fs.mkdir(categoryPath, { recursive: true });
    }
  }

  /**
   * Download all found CDN files
   */
  async downloadAllFiles() {
    console.log('⬇️  Downloading CDN files...');
    
    const urls = Array.from(this.foundUrls);
    const chunks = this.chunkArray(urls, CONFIG.maxConcurrentDownloads);
    
    for (const chunk of chunks) {
      const downloadPromises = chunk.map(url => this.downloadFile(url));
      await Promise.allSettled(downloadPromises);
    }
    
    console.log(`✅ Downloaded ${this.downloadedFiles.size} files`);
    if (this.failedDownloads.size > 0) {
      console.warn(`⚠️  Failed to download ${this.failedDownloads.size} files`);
    }
  }

  /**
   * Download individual file
   */
  async downloadFile(url) {
    try {
      console.log(`📥 Downloading: ${url}`);
      
      const urlObj = new URL(url);
      const fileName = this.generateFileName(url);
      const fileCategory = this.getFileCategory(fileName);
      const localPath = path.join(CONFIG.assetsDir, fileCategory, fileName);
      const fullLocalPath = path.join(this.projectRoot, localPath);
      
      // Download file
      const fileBuffer = await this.httpDownload(url);
      
      // Calculate checksum
      const checksum = crypto.createHash('sha256').update(fileBuffer).digest('hex');
      this.checksums.set(url, checksum);
      
      // Write file
      await fs.writeFile(fullLocalPath, fileBuffer);
      
      // Store mapping
      this.downloadedFiles.set(url, localPath);
      
      console.log(`✅ Downloaded: ${fileName}`);
      
    } catch (error) {
      console.error(`❌ Failed to download ${url}:`, error.message);
      this.failedDownloads.add(url);
    }
  }

  /**
   * HTTP download with timeout
   */
  httpDownload(url) {
    return new Promise((resolve, reject) => {
      const urlObj = new URL(url);
      const client = urlObj.protocol === 'https:' ? https : http;
      
      const timeout = setTimeout(() => {
        reject(new Error('Download timeout'));
      }, CONFIG.downloadTimeout);
      
      const request = client.get(url, (response) => {
        clearTimeout(timeout);
        
        if (response.statusCode !== 200) {
          reject(new Error(`HTTP ${response.statusCode}: ${response.statusMessage}`));
          return;
        }
        
        const chunks = [];
        response.on('data', chunk => chunks.push(chunk));
        response.on('end', () => resolve(Buffer.concat(chunks)));
        response.on('error', reject);
      });
      
      request.on('error', (error) => {
        clearTimeout(timeout);
        reject(error);
      });
      
      request.setTimeout(CONFIG.downloadTimeout, () => {
        request.destroy();
        reject(new Error('Request timeout'));
      });
    });
  }

  /**
   * Generate safe filename from URL
   */
  generateFileName(url) {
    const urlObj = new URL(url);
    let fileName = path.basename(urlObj.pathname);
    
    // If no filename in URL, generate one
    if (!fileName || !path.extname(fileName)) {
      const hash = crypto.createHash('md5').update(url).digest('hex').substring(0, 8);
      const ext = this.guessExtensionFromUrl(url);
      fileName = `file_${hash}${ext}`;
    }
    
    // Sanitize filename
    fileName = fileName.replace(/[^a-zA-Z0-9._-]/g, '_');
    
    return fileName;
  }

  /**
   * Guess file extension from URL or content type
   */
  guessExtensionFromUrl(url) {
    // Try to extract from URL path
    const urlObj = new URL(url);
    const pathExt = path.extname(urlObj.pathname);
    if (pathExt) return pathExt;
    
    // Default extensions based on common CDN patterns
    if (url.includes('image') || url.includes('img')) return '.jpg';
    if (url.includes('style') || url.includes('css')) return '.css';
    if (url.includes('script') || url.includes('js')) return '.js';
    if (url.includes('font')) return '.woff2';
    
    return '.bin'; // fallback
  }

  /**
   * Determine file category based on extension
   */
  getFileCategory(fileName) {
    const ext = path.extname(fileName).toLowerCase();
    
    for (const [category, extensions] of Object.entries(CONFIG.fileTypeMapping)) {
      if (extensions.includes(ext)) {
        return category;
      }
    }
    
    return 'misc'; // fallback category
  }

  /**
   * Update all file references to point to local files
   */
  async updateAllReferences() {
    console.log('🔄 Updating file references...');
    
    const updateDir = async (dirPath) => {
      const entries = await fs.readdir(dirPath, { withFileTypes: true });
      
      for (const entry of entries) {
        const fullPath = path.join(dirPath, entry.name);
        
        if (entry.isDirectory()) {
          if (!['node_modules', '.git', 'dist', 'build', '.next', 'src/assets'].includes(entry.name)) {
            await updateDir(fullPath);
          }
        } else if (entry.isFile()) {
          const ext = path.extname(entry.name).toLowerCase();
          if (CONFIG.scanExtensions.includes(ext)) {
            await this.updateFileReferences(fullPath);
          }
        }
      }
    };
    
    await updateDir(this.projectRoot);
  }

  /**
   * Update references in individual file
   */
  async updateFileReferences(filePath) {
    try {
      let content = await fs.readFile(filePath, 'utf8');
      let hasChanges = false;
      
      for (const [originalUrl, localPath] of this.downloadedFiles) {
        if (content.includes(originalUrl)) {
          // Convert to relative path from the file's location
          const relativePath = this.getRelativePath(filePath, localPath);
          content = content.replace(new RegExp(this.escapeRegExp(originalUrl), 'g'), relativePath);
          hasChanges = true;
        }
      }
      
      if (hasChanges) {
        await fs.writeFile(filePath, content, 'utf8');
        console.log(`✅ Updated references in: ${path.relative(this.projectRoot, filePath)}`);
      }
      
    } catch (error) {
      console.warn(`⚠️  Could not update file ${filePath}:`, error.message);
    }
  }

  /**
   * Get relative path from source file to target asset
   */
  getRelativePath(fromFile, toAsset) {
    const fromDir = path.dirname(fromFile);
    const toPath = path.join(this.projectRoot, toAsset);
    let relativePath = path.relative(fromDir, toPath);
    
    // Ensure forward slashes for web compatibility
    relativePath = relativePath.replace(/\\/g, '/');
    
    // Add ./ prefix if not already present
    if (!relativePath.startsWith('./') && !relativePath.startsWith('../')) {
      relativePath = './' + relativePath;
    }
    
    return relativePath;
  }

  /**
   * Escape string for use in RegExp
   */
  escapeRegExp(string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
  }

  /**
   * Generate mapping log file
   */
  async generateMappingLog() {
    console.log('📝 Generating mapping log...');
    
    const logContent = {
      timestamp: new Date().toISOString(),
      summary: {
        totalUrlsFound: this.foundUrls.size,
        successfulDownloads: this.downloadedFiles.size,
        failedDownloads: this.failedDownloads.size
      },
      mappings: Object.fromEntries(this.downloadedFiles),
      checksums: Object.fromEntries(this.checksums),
      failedUrls: Array.from(this.failedDownloads)
    };
    
    await fs.writeFile(this.logFile, JSON.stringify(logContent, null, 2), 'utf8');
    console.log(`📄 Mapping log saved to: ${this.logFile}`);
  }

  /**
   * Generate summary report
   */
  generateSummaryReport() {
    console.log('\n📊 SUMMARY REPORT');
    console.log('==================');
    console.log(`Total CDN URLs found: ${this.foundUrls.size}`);
    console.log(`Successfully downloaded: ${this.downloadedFiles.size}`);
    console.log(`Failed downloads: ${this.failedDownloads.size}`);
    
    if (this.failedDownloads.size > 0) {
      console.log('\n❌ Failed URLs:');
      this.failedDownloads.forEach(url => console.log(`  - ${url}`));
    }
    
    console.log(`\n📁 Assets organized in: ${CONFIG.assetsDir}/`);
    console.log(`📄 Detailed log: ${path.basename(this.logFile)}`);
  }

  /**
   * Utility function to chunk array
   */
  chunkArray(array, chunkSize) {
    const chunks = [];
    for (let i = 0; i < array.length; i += chunkSize) {
      chunks.push(array.slice(i, i + chunkSize));
    }
    return chunks;
  }
}

// Run the script if called directly
const scriptPath = fileURLToPath(import.meta.url);
const mainPath = process.argv[1];

if (scriptPath === mainPath) {
  console.log('🚀 Starting CDN Internalization Script...');
  const internalizer = new CDNInternalizer();
  internalizer.run().catch(console.error);
}

export default CDNInternalizer;
# Exclude Node.js scripts that use server-side modules
scripts/

# Development files
.env.example
*.md
README.md
DEPLOYMENT.md

# Development tools
.vscode/
.idea/

# Test files
__tests__/
*.test.js
*.test.ts
*.spec.js
*.spec.ts

# Documentation
docs/

# Git files
.git/
.gitignore

# CI/CD files
.github/

# Local development
.env.local
.env.development.local
.env.test.local
.env.production.local

# Cache directories
.cache/
.parcel-cache/

# Temporary files
tmp/
temp/

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
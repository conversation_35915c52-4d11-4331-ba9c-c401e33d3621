/**
 * Comprehensive Website Testing Script
 * The Ice Box - Premier Hockey Equipment
 * 
 * This script performs automated testing of website functionality
 * Run in browser console while on http://localhost:5173/
 */

// Test Results Object
const testResults = {
  pageLoad: {},
  navigation: {},
  responsive: {},
  interactive: {},
  anchors: {},
  assets: {},
  accessibility: {},
  performance: {}
};

// Utility Functions
function logTest(category, test, result, details = '') {
  const status = result ? '✅ PASS' : '❌ FAIL';
  console.log(`${status} [${category}] ${test}${details ? ' - ' + details : ''}`);
  
  if (!testResults[category]) testResults[category] = {};
  testResults[category][test] = { result, details };
}

function waitFor(ms) {
  return new Promise(resolve => setTimeout(resolve, ms));
}

// Test Functions
async function testPageLoad() {
  console.log('\n🔍 Testing Page Load & Initial Render...');
  
  // Test if main elements exist
  const header = document.querySelector('header');
  const hero = document.querySelector('#home');
  const footer = document.querySelector('footer');
  
  logTest('pageLoad', 'Header exists', !!header);
  logTest('pageLoad', 'Hero section exists', !!hero);
  logTest('pageLoad', 'Footer exists', !!footer);
  
  // Test for console errors
  const originalError = console.error;
  let errorCount = 0;
  console.error = (...args) => {
    errorCount++;
    originalError.apply(console, args);
  };
  
  await waitFor(1000);
  logTest('pageLoad', 'No console errors', errorCount === 0, `${errorCount} errors found`);
  console.error = originalError;
  
  // Test CSS loading
  const computedStyle = window.getComputedStyle(document.body);
  const hasStyles = computedStyle.backgroundColor !== 'rgba(0, 0, 0, 0)';
  logTest('pageLoad', 'CSS styles loaded', hasStyles);
}

async function testNavigation() {
  console.log('\n🧭 Testing Navigation Functionality...');
  
  // Test desktop navigation
  const desktopNav = document.querySelector('nav.hidden.md\\:flex');
  const navLinks = desktopNav ? desktopNav.querySelectorAll('a') : [];
  logTest('navigation', 'Desktop navigation exists', !!desktopNav);
  logTest('navigation', 'Navigation links present', navLinks.length > 0, `${navLinks.length} links found`);
  
  // Test mobile menu button
  const mobileMenuButton = document.querySelector('button');
  logTest('navigation', 'Mobile menu button exists', !!mobileMenuButton);
  
  // Test mobile menu toggle
  if (mobileMenuButton) {
    const initialMobileMenu = document.querySelector('.md\\:hidden .flex-col');
    const initiallyVisible = initialMobileMenu && !initialMobileMenu.hidden;
    
    // Click to toggle
    mobileMenuButton.click();
    await waitFor(100);
    
    const afterClickMenu = document.querySelector('.md\\:hidden .flex-col');
    const afterClickVisible = afterClickMenu && !afterClickMenu.hidden;
    
    logTest('navigation', 'Mobile menu toggles', initiallyVisible !== afterClickVisible);
    
    // Click again to close
    mobileMenuButton.click();
    await waitFor(100);
  }
}

async function testInteractiveElements() {
  console.log('\n🎯 Testing Interactive Elements...');
  
  // Test hero buttons
  const heroButtons = document.querySelectorAll('#home button');
  logTest('interactive', 'Hero CTA buttons exist', heroButtons.length >= 2, `${heroButtons.length} buttons found`);
  
  // Test hover effects (simulate)
  heroButtons.forEach((button, index) => {
    const hasHoverClass = button.className.includes('hover:');
    logTest('interactive', `Button ${index + 1} has hover effects`, hasHoverClass);
  });
  
  // Test service cards hover effects
  const serviceCards = document.querySelectorAll('#services .transform');
  logTest('interactive', 'Service cards have hover effects', serviceCards.length > 0, `${serviceCards.length} cards found`);
}

async function testAnchorLinks() {
  console.log('\n⚓ Testing Anchor Link Navigation...');
  
  const sections = ['home', 'about', 'services', 'equipment', 'contact'];
  
  sections.forEach(sectionId => {
    const section = document.getElementById(sectionId);
    const navLink = document.querySelector(`a[href="#${sectionId}"]`);
    
    logTest('anchors', `Section #${sectionId} exists`, !!section);
    logTest('anchors', `Nav link to #${sectionId} exists`, !!navLink);
  });
}

async function testAssets() {
  console.log('\n🖼️ Testing Assets & Media...');
  
  // Test hero background image
  const heroSection = document.querySelector('#home > div');
  if (heroSection) {
    const bgImage = window.getComputedStyle(heroSection).backgroundImage;
    const hasBackgroundImage = bgImage && bgImage !== 'none';
    logTest('assets', 'Hero background image loaded', hasBackgroundImage);
  }
  
  // Test Lucide icons
  const icons = document.querySelectorAll('svg');
  logTest('assets', 'Icons rendered', icons.length > 0, `${icons.length} icons found`);
  
  // Test for broken images
  const images = document.querySelectorAll('img');
  let brokenImages = 0;
  images.forEach(img => {
    if (!img.complete || img.naturalHeight === 0) {
      brokenImages++;
    }
  });
  logTest('assets', 'No broken images', brokenImages === 0, `${brokenImages} broken images`);
}

async function testResponsive() {
  console.log('\n📱 Testing Responsive Design...');
  
  const originalWidth = window.innerWidth;
  
  // Test mobile breakpoint
  const mobileNav = document.querySelector('.md\\:hidden');
  const desktopNav = document.querySelector('.hidden.md\\:flex');
  
  logTest('responsive', 'Mobile navigation exists', !!mobileNav);
  logTest('responsive', 'Desktop navigation exists', !!desktopNav);
  
  // Test grid layouts
  const serviceGrid = document.querySelector('#services .grid');
  const equipmentGrid = document.querySelector('#equipment .grid');
  
  logTest('responsive', 'Services grid layout', !!serviceGrid);
  logTest('responsive', 'Equipment grid layout', !!equipmentGrid);
}

async function testAccessibility() {
  console.log('\n♿ Testing Accessibility...');
  
  // Test semantic HTML
  const header = document.querySelector('header');
  const main = document.querySelector('main') || document.querySelector('#home');
  const footer = document.querySelector('footer');
  const nav = document.querySelector('nav');
  
  logTest('accessibility', 'Header element exists', !!header);
  logTest('accessibility', 'Main content area exists', !!main);
  logTest('accessibility', 'Footer element exists', !!footer);
  logTest('accessibility', 'Navigation element exists', !!nav);
  
  // Test button accessibility
  const buttons = document.querySelectorAll('button');
  let accessibleButtons = 0;
  buttons.forEach(button => {
    if (button.textContent.trim() || button.getAttribute('aria-label')) {
      accessibleButtons++;
    }
  });
  logTest('accessibility', 'Buttons have accessible text', accessibleButtons === buttons.length);
  
  // Test color contrast (basic check)
  const body = document.body;
  const bodyStyles = window.getComputedStyle(body);
  const hasGoodContrast = bodyStyles.backgroundColor !== bodyStyles.color;
  logTest('accessibility', 'Basic color contrast', hasGoodContrast);
}

async function testPerformance() {
  console.log('\n⚡ Testing Performance...');
  
  // Test page load time (if available)
  if (window.performance && window.performance.timing) {
    const loadTime = window.performance.timing.loadEventEnd - window.performance.timing.navigationStart;
    logTest('performance', 'Page load time < 3s', loadTime < 3000, `${loadTime}ms`);
  }
  
  // Test DOM size
  const domSize = document.querySelectorAll('*').length;
  logTest('performance', 'Reasonable DOM size', domSize < 1000, `${domSize} elements`);
}

// Main Test Runner
async function runAllTests() {
  console.clear();
  console.log('🚀 Starting Comprehensive Website Testing...');
  console.log('Website: The Ice Box - Premier Hockey Equipment');
  console.log('URL: ' + window.location.href);
  console.log('Timestamp: ' + new Date().toISOString());
  
  try {
    await testPageLoad();
    await testNavigation();
    await testInteractiveElements();
    await testAnchorLinks();
    await testAssets();
    await testResponsive();
    await testAccessibility();
    await testPerformance();
    
    console.log('\n📊 Test Summary:');
    
    let totalTests = 0;
    let passedTests = 0;
    
    Object.keys(testResults).forEach(category => {
      const categoryTests = testResults[category];
      const categoryTotal = Object.keys(categoryTests).length;
      const categoryPassed = Object.values(categoryTests).filter(test => test.result).length;
      
      totalTests += categoryTotal;
      passedTests += categoryPassed;
      
      console.log(`${category}: ${categoryPassed}/${categoryTotal} passed`);
    });
    
    console.log(`\n🎯 Overall: ${passedTests}/${totalTests} tests passed (${Math.round(passedTests/totalTests*100)}%)`);
    
    // Return results for external use
    return {
      summary: {
        total: totalTests,
        passed: passedTests,
        percentage: Math.round(passedTests/totalTests*100)
      },
      details: testResults
    };
    
  } catch (error) {
    console.error('❌ Testing failed:', error);
    return { error: error.message };
  }
}

// Auto-run if in browser
if (typeof window !== 'undefined') {
  console.log('🔧 Test script loaded. Run runAllTests() to start testing.');
}

// Export for Node.js if needed
if (typeof module !== 'undefined' && module.exports) {
  module.exports = { runAllTests, testResults };
}
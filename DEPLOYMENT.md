# Deployment Guide - The Ice Box

This document outlines the production deployment configuration for The Ice Box hockey equipment website.

## 🚀 Netlify Deployment

### Prerequisites

- Node.js 18+ (specified in `package.json` engines)
- npm 8+
- GitHub repository connected to Netlify

### Build Configuration

**Build Command:** `npm run build`
**Publish Directory:** `dist/client`
**Node Version:** 18

### Environment Variables

Set the following environment variables in Netlify dashboard:

```bash
# Required for Google Maps integration
VITE_GOOGLE_MAPS_API_KEY=your_google_maps_api_key_here

# Build environment
NODE_ENV=production
CI=true
```

### Deployment Settings

The project includes a comprehensive `netlify.toml` configuration with:

- **SPA Routing:** Proper redirects for React Router
- **Security Headers:** CSP, CORS, and security best practices
- **Cache Optimization:** Long-term caching for assets, no-cache for HTML
- **Google Maps CSP:** Configured for Maps API integration
- **Build Contexts:** Different settings for production, staging, and branch deploys

## 📁 Project Structure

```
├── dist/                    # Build output (auto-generated)
│   ├── assets/             # Bundled CSS, JS, images
│   └── index.html          # Main HTML file
├── src/
│   ├── react-app/          # React application source
│   └── assets/             # Static assets
├── netlify.toml            # Netlify configuration
├── package.json            # Dependencies and scripts
├── vite.config.ts          # Vite build configuration
└── tsconfig.json           # TypeScript configuration
```

## 🔧 Build Process

### Local Development
```bash
npm install
npm run dev
```

### Production Build
```bash
npm run build:prod
```

### Build Steps
1. **Type Checking:** TypeScript compilation and validation
2. **Clean:** Remove previous build artifacts
3. **Vite Build:** Bundle React app with optimizations
4. **Asset Processing:** Image optimization, CSS/JS minification
5. **Output:** Static files in `dist/client/`

## 🔒 Security Features

- **Content Security Policy:** Configured for Google Maps API
- **Security Headers:** X-Frame-Options, X-Content-Type-Options, etc.
- **Environment Variables:** Sensitive data excluded from build
- **Asset Integrity:** Immutable caching with content hashing

## 🌐 Performance Optimizations

- **Code Splitting:** Automatic chunking by Vite
- **Asset Optimization:** Image compression and format optimization
- **Caching Strategy:** Long-term caching for assets, fresh HTML
- **Bundle Analysis:** Chunk size warnings configured
- **Lighthouse Integration:** Performance monitoring via Netlify plugin

## 📊 Monitoring & Analytics

- **Lighthouse Scores:** Automated performance auditing
- **Build Logs:** Comprehensive logging in Netlify dashboard
- **Error Tracking:** React Error Boundaries implemented

## 🔄 CI/CD Workflow

### GitHub Integration
1. **Push to main:** Triggers production deployment
2. **Pull Requests:** Creates deploy previews
3. **Branch Deploys:** Automatic staging environments

### Build Contexts
- **Production:** `main` branch with production optimizations
- **Staging:** Deploy previews with staging environment
- **Development:** Branch deploys with development settings

## 🛠 Troubleshooting

### Common Issues

**Build Failures:**
- Check Node.js version (must be 18+)
- Verify all environment variables are set
- Run `npm run type-check` locally

**Routing Issues:**
- Ensure `netlify.toml` redirects are configured
- Check React Router configuration

**Asset Loading:**
- Verify relative paths in imports
- Check Vite asset handling configuration

### Debug Commands
```bash
# Type checking
npm run type-check

# Lint checking
npm run lint

# Local preview of production build
npm run preview

# Clean build artifacts
npm run clean
```

## 📋 Deployment Checklist

- [ ] Environment variables configured in Netlify
- [ ] Domain/subdomain configured
- [ ] SSL certificate enabled
- [ ] Build command set to `npm run build`
- [ ] Publish directory set to `dist/client`
- [ ] Node version set to 18
- [ ] Google Maps API key configured
- [ ] Security headers verified
- [ ] Performance scores acceptable (>80)
- [ ] All routes working correctly
- [ ] Error pages configured

## 🔗 Useful Links

- [Netlify Documentation](https://docs.netlify.com/)
- [Vite Deployment Guide](https://vitejs.dev/guide/static-deploy.html)
- [React Router Deployment](https://reactrouter.com/en/main/guides/deploying)
- [Google Maps API Setup](https://developers.google.com/maps/documentation/javascript/get-api-key)

---

**Last Updated:** $(date)
**Deployment Status:** ✅ Production Ready
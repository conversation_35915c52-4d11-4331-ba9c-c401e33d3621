import Header from '@/react-app/components/Header';
import Hero from '@/react-app/components/Hero';
import About from '@/react-app/components/About';
import Services from '@/react-app/components/Services';
import Technology from '@/react-app/components/Technology';
import Equipment from '@/react-app/components/Equipment';
import SkateServices from '@/react-app/components/SkateServices';
import Brands from '@/react-app/components/Brands';
import Footer from '@/react-app/components/Footer';
import ErrorBoundary from '@/react-app/components/ErrorBoundary';

export default function Home() {
  return (
    <div className="min-h-screen bg-slate-900">
      <Header />
      <Hero />
      <ErrorBoundary fallback={
        <section id="about" className="py-20 bg-slate-800">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
            <h2 className="text-3xl font-bold text-white mb-8">About Us</h2>
            <p className="text-gray-300 mb-8">
              We're The Ice Box - your premier destination for hockey equipment and services.
            </p>
            <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 max-w-md mx-auto">
              <p className="text-yellow-800 text-sm">
                Map temporarily unavailable. Please contact us directly for location information.
              </p>
            </div>
          </div>
        </section>
      }>
        <About />
      </ErrorBoundary>
      <Services />
      <Technology />
      <Equipment />
      <SkateServices />
      <Brands />
      <Footer />
    </div>
  );
}

import { Menu, X } from 'lucide-react';
import { useState } from 'react';
import Logo from './Logo';

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  return (
    <header className="bg-slate-900/90 backdrop-blur-sm fixed w-full top-0 z-50 border-b border-slate-700/30">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-2">
        <div className="flex justify-between items-center h-20 sm:h-24">
          {/* Logo */}
          <div className="flex items-center">
            <Logo size="medium" clickable={true} />
          </div>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex space-x-8">
            <a href="#home" className="text-white hover:text-blue-400 transition-colors">Home</a>
            <a href="#about" className="text-white hover:text-blue-400 transition-colors">About</a>
            <a href="#services" className="text-white hover:text-blue-400 transition-colors">Services</a>
            <a href="#equipment" className="text-white hover:text-blue-400 transition-colors">Equipment</a>
            <a href="#contact" className="text-white hover:text-blue-400 transition-colors">Contact</a>
          </nav>

          {/* Mobile menu button */}
          <div className="md:hidden">
            <button
              onClick={() => setIsMenuOpen(!isMenuOpen)}
              className="text-white hover:text-blue-400"
            >
              {isMenuOpen ? <X size={24} /> : <Menu size={24} />}
            </button>
          </div>
        </div>

        {/* Mobile Navigation */}
        {isMenuOpen && (
          <div className="md:hidden py-4 border-t border-slate-700/30">
            <nav className="flex flex-col space-y-2">
              <a href="#home" className="text-white hover:text-blue-400 transition-colors py-2">Home</a>
              <a href="#about" className="text-white hover:text-blue-400 transition-colors py-2">About</a>
              <a href="#services" className="text-white hover:text-blue-400 transition-colors py-2">Services</a>
              <a href="#equipment" className="text-white hover:text-blue-400 transition-colors py-2">Equipment</a>
              <a href="#contact" className="text-white hover:text-blue-400 transition-colors py-2">Contact</a>
            </nav>
          </div>
        )}
      </div>
    </header>
  );
}

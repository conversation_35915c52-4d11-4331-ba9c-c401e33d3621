import { Settings, Package, Zap, Wrench } from 'lucide-react';

const services = [
  {
    icon: Settings,
    title: "Professional Equipment Fitting",
    description: "Expert fitting services to ensure optimal performance and comfort. Our trained staff will help you find the perfect equipment that matches your playing style and body measurements."
  },
  {
    icon: Package,
    title: "Equipment Packages",
    description: "Complete equipment packages for players at every level. From youth starter sets to professional-grade gear, we offer comprehensive solutions that save you time and money."
  },
  {
    icon: Zap,
    title: "Customization",
    description: "Personalize your gear with custom colors, logos, and specifications. We offer professional customization services to make your equipment uniquely yours while maintaining peak performance."
  },
  {
    icon: Wrench,
    title: "Equipment Maintenance",
    description: "Keep your gear in top condition with our maintenance and repair services. From skate sharpening to equipment reconditioning, we ensure your gear performs when you need it most."
  }
];

export default function Services() {
  return (
    <section id="services" className="py-20 bg-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Our Services</h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            Professional services designed to help you elevate your game and experience hockey 
            like never before. From fitting to performance consulting.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
          {services.map((service, index) => (
            <div key={index} className="bg-slate-800 p-6 rounded-xl shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 border border-slate-700/50">
              <div className="bg-blue-600 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <service.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-semibold text-white mb-3">{service.title}</h3>
              <p className="text-gray-300 leading-relaxed">{service.description}</p>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

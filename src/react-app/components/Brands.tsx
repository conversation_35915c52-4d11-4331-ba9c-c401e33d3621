import ccmLogo from '../../assets/images/CCM-Logo_2.webp';
import bauer<PERSON>ogo from '../../assets/images/Bauer_4.webp';
import warriorLogo from '../../assets/images/warrior.webp';
import trueTempLogo from '../../assets/images/True_Temper_Hockey-Baseball_CatIcon.webp';
import sherwood<PERSON>ogo from '../../assets/images/SherWood_3_2.webp';
import missionLogo from '../../assets/images/Mission-Icon-2023_1.webp';
import winnwellLogo from '../../assets/images/Winnwell-Brand-Icon_1.webp';
import marsbladeLogo from '../../assets/images/Marsblade_1.webp';
import biosteelLogo from '../../assets/images/Biosteel_1.webp';
import howies<PERSON>ogo from '../../assets/images/Howies_Hockey_Tape_Category_Icon_1.webp';

export default function Brands() {
  const brands = [
    { name: "<PERSON><PERSON>", logo: ccm<PERSON><PERSON> },
    { name: "<PERSON>", logo: b<PERSON><PERSON><PERSON> },
    { name: "<PERSON>", logo: warrior<PERSON><PERSON> },
    { name: "<PERSON> Temper", logo: trueTemp<PERSON><PERSON> },
    { name: "<PERSON>", logo: sherwoodLogo },
    { name: "Mission", logo: missionLogo },
    { name: "Winnwell", logo: winnwellLogo },
    { name: "Marsblade", logo: marsbladeLogo },
    { name: "Biosteel", logo: biosteelLogo },
    { name: "Howies", logo: howiesLogo }
  ];

  return (
    <section className="py-20 bg-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Brands We Are Proud to Offer</h2>
          <p className="text-xl text-gray-300 max-w-2xl mx-auto">
            We carry all the leading hockey brands to ensure you have access to the best equipment available.
          </p>
        </div>

        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-8">
          {brands.map((brand, index) => (
            <div key={index} className="bg-white p-6 rounded-lg shadow-lg hover:shadow-xl transition-all duration-200 transform hover:scale-105 flex items-center justify-center">
              <img 
                src={brand.logo} 
                alt={brand.name}
                className="max-w-full max-h-12 object-contain filter grayscale hover:grayscale-0 transition-all duration-200"
              />
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

import { MouseEvent } from 'react';

interface LogoProps {
  /** Size variant for different use cases */
  size?: 'small' | 'medium' | 'large' | 'extra-large';
  /** Whether the logo should be clickable and link to homepage */
  clickable?: boolean;
  /** Additional CSS classes */
  className?: string;
  /** Custom click handler (overrides default homepage navigation) */
  onClick?: (event: MouseEvent<HTMLElement>) => void;
}

/**
 * Reusable Logo component for The Ice Box brand
 * Displays the company logo with proper accessibility and responsive sizing
 */
export default function Logo({ 
  size = 'medium', 
  clickable = true, 
  className = '', 
  onClick 
}: LogoProps) {
  
  /**
   * <PERSON>le logo click - navigate to homepage by default
   */
  const handleClick = (event: MouseEvent<HTMLElement>) => {
    if (onClick) {
      onClick(event);
    } else if (clickable) {
      // Scroll to top of page (homepage)
      window.scrollTo({ top: 0, behavior: 'smooth' });
      // Update URL hash to home
      window.location.hash = '#home';
    }
  };

  /**
   * Get responsive size classes based on size prop
   */
  const getSizeClasses = () => {
    switch (size) {
      case 'small':
        return 'h-12 w-auto sm:h-14'; // 48px mobile, 56px desktop
      case 'large':
        return 'h-20 w-auto sm:h-24 md:h-28'; // 80px mobile, 96px tablet, 112px desktop
      case 'extra-large':
        return 'h-24 w-auto sm:h-32 md:h-36'; // 96px mobile, 128px tablet, 144px desktop
      case 'medium':
      default:
        return 'h-16 w-auto sm:h-18 md:h-20'; // 64px mobile, 72px tablet, 80px desktop
    }
  };

  const logoElement = (
    <img
      src="/src/assets/images/iceboxlogo.png"
      alt="The Ice Box - Premier Hockey Equipment"
      className={`${getSizeClasses()} object-contain transition-opacity duration-200 hover:opacity-90 ${className}`}
      loading="eager"
      decoding="async"
    />
  );

  // Return clickable version if clickable prop is true
  if (clickable) {
    return (
      <button
        onClick={handleClick}
        className="focus:outline-none focus:ring-2 focus:ring-blue-400 focus:ring-offset-2 focus:ring-offset-slate-900 rounded-sm transition-all duration-200"
        aria-label="The Ice Box - Return to homepage"
        type="button"
      >
        {logoElement}
      </button>
    );
  }

  // Return non-clickable version
  return logoElement;
}
import { useEffect, useState, useRef } from 'react';
import { useSafeDOM } from '@/react-app/hooks/useSafeDOM';

// Global type declarations for Google Maps API
declare global {
  interface Window {
    google: any;
    initMap?: () => void;
    googleMapsLoaded?: boolean;
  }
}

export default function About() {
  const [mapError, setMapError] = useState<string | null>(null);
  const [isMapLoaded, setIsMapLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  const [retryCount, setRetryCount] = useState(0);
  const maxRetries = 3;
  const mapRef = useRef<any>(null);
  const mapElementRef = useRef<HTMLDivElement | null>(null);
  const scriptRef = useRef<HTMLScriptElement | null>(null);
  const cleanupFunctionsRef = useRef<(() => void)[]>([]);
  const callbackNameRef = useRef<string>('');
  
  // Use safe DOM operations hook
  const { safeRemoveChild, safeAddEventListener, safeSetAttribute, safeAppendChild } = useSafeDOM();

  useEffect(() => {
    let isMounted = true;

    // Manual retry event listener
    const handleRetryMap = () => {
      if (isMounted) {
        initializeMap();
      }
    };
    
    window.addEventListener('retryMap', handleRetryMap);

    /**
     * Initialize Google Maps with comprehensive error handling and cleanup
     */
    const initializeMap = () => {
      try {
        // Check if component is still mounted
        if (!isMounted) return;

        if (!window.google || !window.google.maps) {
          throw new Error('Google Maps API failed to load');
        }

        const mapElement = mapElementRef.current;
        if (!mapElement) {
          throw new Error('Map container not found');
        }

        // Prevent multiple map instances
        if (mapRef.current) {
          console.warn('Map already initialized, skipping...');
          return;
        }

        // Create map with comprehensive options
        const map = new window.google.maps.Map(mapElement, {
          center: { lat: 33.8541, lng: -118.3012 }, // Harbor City, CA coordinates
          zoom: 15,
          mapTypeControl: true,
          zoomControl: true,
          zoomControlOptions: {
            position: window.google.maps.ControlPosition.RIGHT_CENTER,
          },
          scaleControl: true,
          streetViewControl: true,
          streetViewControlOptions: {
            position: window.google.maps.ControlPosition.RIGHT_BOTTOM,
          },
          fullscreenControl: true,
          // Custom styling for better integration
          styles: [
            {
              featureType: 'all',
              elementType: 'geometry.fill',
              stylers: [{ color: '#1e293b' }]
            },
            {
              featureType: 'water',
              elementType: 'geometry',
              stylers: [{ color: '#0f172a' }]
            }
          ]
        });

        // Store map reference for cleanup
        mapRef.current = map;

        // Add custom marker with info window
        const marker = new window.google.maps.Marker({
          position: { lat: 33.8541, lng: -118.3012 },
          map: map,
          title: 'The Ice Box - Premier Hockey Equipment',
          animation: window.google.maps.Animation.DROP,
          icon: {
            url: 'data:image/svg+xml;charset=UTF-8,' + encodeURIComponent(`
              <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
                <circle cx="20" cy="20" r="18" fill="#3b82f6" stroke="#ffffff" stroke-width="2"/>
                <text x="20" y="26" text-anchor="middle" fill="white" font-family="Arial" font-size="12" font-weight="bold">ICE</text>
              </svg>
            `),
            scaledSize: new window.google.maps.Size(40, 40),
            anchor: new window.google.maps.Point(20, 20)
          }
        });

        // Create info window with business details
        const infoWindow = new window.google.maps.InfoWindow({
          content: `
            <div style="padding: 10px; max-width: 300px;">
              <h3 style="margin: 0 0 10px 0; color: #1e293b; font-size: 18px; font-weight: bold;">The Ice Box</h3>
              <p style="margin: 0 0 8px 0; color: #475569; font-size: 14px;">Premier Hockey Equipment</p>
              <p style="margin: 0 0 8px 0; color: #475569; font-size: 14px;">
                <strong>Address:</strong><br>
                23770 S Western Ave<br>
                Harbor City, CA 90710
              </p>
              <p style="margin: 0 0 8px 0; color: #475569; font-size: 14px;">
                <strong>Phone:</strong> (*************<br>
                <strong>Email:</strong> <EMAIL>
              </p>
              <p style="margin: 0 0 10px 0; color: #475569; font-size: 14px;">
                <strong>Hours:</strong><br>
                Mon-Fri: 10am - 8pm<br>
                Saturday: 9am - 6pm<br>
                Sunday: 11am - 5pm
              </p>
              <a href="https://maps.google.com/maps?daddr=23770+S+Western+Ave,+Harbor+City,+CA+90710" 
                 target="_blank" 
                 style="display: inline-block; background: #3b82f6; color: white; padding: 8px 16px; text-decoration: none; border-radius: 4px; font-size: 14px; font-weight: bold;">
                Get Directions
              </a>
            </div>
          `
        });

        // Show info window when marker is clicked
        marker.addListener('click', () => {
          infoWindow.open(map, marker);
        });

        // Handle window resize for responsiveness
        const handleResize = () => {
          if (mapRef.current && window.google && window.google.maps) {
            window.google.maps.event.trigger(mapRef.current, 'resize');
            mapRef.current.setCenter({ lat: 33.8541, lng: -118.3012 });
          }
        };

        window.addEventListener('resize', handleResize);
        cleanupFunctionsRef.current.push(() => {
          window.removeEventListener('resize', handleResize);
        });

        // Add keyboard navigation support using safe DOM operations
         safeSetAttribute(mapElement, 'tabindex', '0');
         safeSetAttribute(mapElement, 'role', 'application');
         safeSetAttribute(mapElement, 'aria-label', 'Interactive map showing The Ice Box location');

        // Keyboard event handlers for accessibility
        const handleKeyDown = (e: Event) => {
          const keyboardEvent = e as KeyboardEvent;
          if (!mapRef.current || !window.google || !window.google.maps) return;
          
          const currentCenter = mapRef.current.getCenter();
          const currentZoom = mapRef.current.getZoom();
          let newLat = currentCenter.lat();
          let newLng = currentCenter.lng();
          const step = 0.001;

          switch (keyboardEvent.key) {
            case 'ArrowUp':
              keyboardEvent.preventDefault();
              newLat += step;
              break;
            case 'ArrowDown':
              keyboardEvent.preventDefault();
              newLat -= step;
              break;
            case 'ArrowLeft':
              keyboardEvent.preventDefault();
              newLng -= step;
              break;
            case 'ArrowRight':
              keyboardEvent.preventDefault();
              newLng += step;
              break;
            case '+':
            case '=':
              keyboardEvent.preventDefault();
              mapRef.current.setZoom(Math.min(currentZoom + 1, 20));
              return;
            case '-':
              keyboardEvent.preventDefault();
              mapRef.current.setZoom(Math.max(currentZoom - 1, 1));
              return;
            case 'Enter':
            case ' ':
              keyboardEvent.preventDefault();
              infoWindow.open(mapRef.current, marker);
              return;
            default:
              return;
          }

          mapRef.current.setCenter({ lat: newLat, lng: newLng });
        };
        
        // Add event listener with safe DOM operations
         const keydownCleanup = safeAddEventListener(mapElement, 'keydown', handleKeyDown);
         if (keydownCleanup) {
           cleanupFunctionsRef.current.push(keydownCleanup);
         }

        if (isMounted) {
          setIsMapLoaded(true);
          setMapError(null);
          setIsLoading(false);
        }

      } catch (error) {
        console.error('Map initialization error:', error);
        if (isMounted) {
          const errorMessage = error instanceof Error ? error.message : 'Failed to load map';
          setMapError(errorMessage);
          setIsMapLoaded(false);
          setIsLoading(false);
          
          // Attempt retry if under max retry limit
          if (retryCount < maxRetries) {
            console.log(`Retrying map initialization (${retryCount + 1}/${maxRetries})...`);
            setTimeout(() => {
              if (isMounted) {
                setRetryCount((prev: number) => prev + 1);
                setIsLoading(true);
                setMapError(null);
                initializeMap();
              }
            }, 2000 * (retryCount + 1)); // Exponential backoff
          }
        }
      }
    };

    /**
     * Create unique callback name to prevent conflicts
     */
    if (!callbackNameRef.current) {
      callbackNameRef.current = `initMap_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }
    const callbackName = callbackNameRef.current;
    
    // Set up global callback for Google Maps API
    (window as any)[callbackName] = initializeMap;

    /**
     * Load Google Maps API script with duplicate prevention
     */
    const loadGoogleMapsScript = () => {
      // Check if script is already loading or loaded
      const existingScript = document.querySelector('script[src*="maps.googleapis.com"]');
      if (existingScript || window.googleMapsLoaded || window.google) {
        if (window.google) {
          // API already loaded, initialize immediately
          initializeMap();
        } else {
          // Script loading, wait for it
          const checkLoaded = setInterval(() => {
            if (window.google) {
              clearInterval(checkLoaded);
              initializeMap();
            }
          }, 100);
          
          cleanupFunctionsRef.current.push(() => {
            clearInterval(checkLoaded);
          });
        }
        return;
      }

      // Create new script element
      const script = document.createElement('script');
      script.src = `https://maps.googleapis.com/maps/api/js?key=${import.meta.env.VITE_GOOGLE_MAPS_API_KEY}&callback=${callbackName}&libraries=places&loading=async`;
      script.async = true;
      script.defer = true;
      script.onerror = (error) => {
         console.error('Google Maps script loading error:', error);
         if (isMounted) {
           setMapError('Failed to load Google Maps API script');
           setIsLoading(false);
           
           // Attempt retry for script loading
           if (retryCount < maxRetries) {
             console.log(`Retrying script load (${retryCount + 1}/${maxRetries})...`);
             setTimeout(() => {
               if (isMounted) {
                 setRetryCount((prev: number) => prev + 1);
                 setMapError(null);
                 setIsLoading(true);
                 // Remove failed script and try again
                 if (scriptRef.current) {
                   const removed = safeRemoveChild(scriptRef.current);
                   if (!removed) {
                     console.warn('Failed to safely remove script element');
                   }
                   scriptRef.current = null;
                 }
                 loadGoogleMapsScript();
               }
             }, 3000 * (retryCount + 1));
           }
         }
       };
      
      scriptRef.current = script;
      // Use safe DOM manipulation for script injection
       const scriptAdded = safeAppendChild(document.head, script);
       if (scriptAdded) {
         window.googleMapsLoaded = true;
       } else {
         console.error('Failed to safely append script to document head');
         if (isMounted) {
           setMapError('Failed to load Google Maps: Script injection failed');
           setIsLoading(false);
         }
       }
    };

    loadGoogleMapsScript();

    /**
     * Comprehensive cleanup function
     */
    return () => {
      isMounted = false;
      
      // Remove retry event listener
      window.removeEventListener('retryMap', handleRetryMap);
      
      // Clean up all event listeners
      cleanupFunctionsRef.current.forEach((cleanup: () => void) => {
        try {
          cleanup();
        } catch (error) {
          console.warn('Cleanup function error:', error);
        }
      });
      cleanupFunctionsRef.current = [];
      
      // Clean up global callback
      if (callbackNameRef.current && (window as any)[callbackNameRef.current]) {
        delete (window as any)[callbackNameRef.current];
      }
      
      // Clean up script element using safe DOM operations
      if (scriptRef.current) {
        const removed = safeRemoveChild(scriptRef.current);
        if (!removed) {
          console.warn('Failed to safely remove script during cleanup');
        }
        scriptRef.current = null;
      }
      
      // Clean up map instance
      if (mapRef.current) {
        try {
          // Clear all listeners on the map
          if (window.google && window.google.maps) {
            window.google.maps.event.clearInstanceListeners(mapRef.current);
          }
          mapRef.current = null;
        } catch (error) {
          console.warn('Map cleanup error:', error);
        }
      }
    };
  }, []);

  return (
    <section id="about" className="py-20 bg-slate-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="grid lg:grid-cols-2 gap-12 items-center">
          {/* About Content */}
          <div>
            <h2 className="text-4xl font-bold text-white mb-8">About The Ice Box</h2>
            
            <div className="space-y-8">
              <div>
                <h3 className="text-xl font-semibold text-blue-400 mb-4">The Ice Box Difference</h3>
                <p className="text-gray-300 leading-relaxed">
                  At The Ice Box we have a passion for hockey and unmatched expertise. 
                  We provide personalized service and high-quality equipment to help 
                  you perform at your best. Our experienced staff understands the 
                  game from the ground up and can guide you to the right equipment 
                  for every level of play.
                </p>
              </div>

              <div>
                <h3 className="text-xl font-semibold text-blue-400 mb-4">Community and Convenience</h3>
                <p className="text-gray-300 leading-relaxed">
                  We're proud to serve the hockey community with expert advice, 
                  professional services, and a comprehensive selection of equipment. 
                  Our shop is your one-stop destination for everything hockey - from 
                  protective gear and sticks to skates and accessories. We're committed 
                  to helping you find the perfect fit for your game and budget, making 
                  sure you're always ready to take the ice with confidence.
                </p>
              </div>
            </div>
          </div>

          {/* Find Us Section with Interactive Map */}
          <div className="bg-slate-900 p-8 rounded-xl shadow-2xl">
            <h3 className="text-2xl font-bold text-white mb-6">Find Us</h3>
            
            {/* Location Details */}
            <div className="space-y-4 text-gray-300 mb-6">
              <p>23770 S Western Ave<br />Harbor City, CA 90710</p>
              <p>Phone: (*************<br />Email: <EMAIL></p>
              <p className="text-sm">
                Monday - Friday: 10am - 8pm<br />
                Saturday: 9am - 6pm<br />
                Sunday: 11am - 5pm
              </p>
            </div>

            {/* Interactive Map Container */}
            <div className="relative">
              <div 
                ref={mapElementRef}
                className="w-full h-80 rounded-lg overflow-hidden border-2 border-slate-700 focus:border-blue-500 focus:outline-none transition-colors"
                style={{ 
                  minHeight: '320px',
                  background: mapError ? '#374151' : '#1e293b'
                }}
              >
                {/* Loading State */}
                {(isLoading || (!isMapLoaded && !mapError)) && (
                  <div className="flex items-center justify-center h-full">
                    <div className="text-center">
                      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto mb-4"></div>
                      <p className="text-gray-400">
                        {retryCount > 0 
                          ? `Retrying... (${retryCount}/${maxRetries})`
                          : 'Loading interactive map...'
                        }
                      </p>
                    </div>
                  </div>
                )}

                {/* Error State */}
                {mapError && !isLoading && (
                  <div className="flex items-center justify-center h-full p-4">
                    <div className="text-center">
                      <div className="text-red-400 mb-4">
                        <svg className="w-12 h-12 mx-auto mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z" />
                        </svg>
                      </div>
                      <p className="text-red-400 mb-2">Map Loading Error</p>
                      <p className="text-gray-400 text-sm mb-4">
                        Unable to load interactive map: {mapError}
                      </p>
                      {retryCount >= maxRetries ? (
                        <div className="space-y-3">
                          <p className="text-gray-500 text-xs">Maximum retry attempts reached</p>
                          <button
                            onClick={() => {
                              setRetryCount(0);
                              setMapError(null);
                              setIsLoading(true);
                              // Trigger re-initialization
                              const event = new CustomEvent('retryMap');
                              window.dispatchEvent(event);
                            }}
                            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium mr-2"
                          >
                            Try Again
                          </button>
                        </div>
                      ) : (
                        <p className="text-gray-500 text-xs mb-4">Retrying automatically...</p>
                      )}
                      <a 
                        href="https://maps.google.com/maps?daddr=23770+S+Western+Ave,+Harbor+City,+CA+90710" 
                        target="_blank" 
                        rel="noopener noreferrer"
                        className="inline-block bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors"
                      >
                        Open in Google Maps
                      </a>
                    </div>
                  </div>
                )}
              </div>

              {/* Map Instructions for Screen Readers */}
              <div className="sr-only">
                <p>Interactive map showing The Ice Box location at 23770 S Western Ave, Harbor City, CA 90710.</p>
                <p>Use arrow keys to pan the map, plus and minus keys to zoom, and Enter or Space to open location details.</p>
              </div>
            </div>

            {/* Get Directions Button */}
            <a 
              href="https://maps.google.com/maps?daddr=23770+S+Western+Ave,+Harbor+City,+CA+90710" 
              target="_blank" 
              rel="noopener noreferrer"
              className="mt-6 bg-blue-600 hover:bg-blue-700 text-white px-6 py-3 rounded-lg font-medium transition-colors w-full inline-block text-center"
            >
              Get Directions
            </a>
          </div>
        </div>
      </div>
    </section>
  );
}

import whiskImage from '../../assets/images/Whisk_cbee896f2a.webp';

export default function Hero() {
  return (
    <section id="home" className="min-h-screen relative flex items-center justify-center pt-20 sm:pt-24">
      {/* Background image */}
      <div
        className="absolute inset-0 bg-cover bg-center bg-no-repeat"
        style={{
          backgroundImage: `url('${whiskImage}')`
        }}
      ></div>
      
      {/* Blur overlay */}
      <div className="absolute inset-0 backdrop-blur-sm bg-black/40"></div>

      <div className="relative z-10 text-center max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
        <h1 className="text-5xl md:text-7xl font-bold text-white mb-6">
          <span className="text-white">The Ice Box</span>
        </h1>
        <h2 className="text-2xl md:text-3xl text-gray-200 mb-8 font-medium">
          Your Hockey Specialists
        </h2>
        <p className="text-lg md:text-xl text-gray-300 max-w-2xl mx-auto mb-12 leading-relaxed">
          We are your shop to professional-grade hockey equipment, expert fitting 
          services, and comprehensive solutions for players at every level. From 
          beginner to pro, we've got you covered.
        </p>
        
        <div className="flex justify-center">
          <a 
            href="https://store.iceboxhockey.com/" 
            target="_blank" 
            rel="noopener noreferrer"
            className="bg-blue-600 hover:bg-blue-700 text-white px-8 py-4 rounded-lg font-semibold transition-all duration-200 transform hover:scale-105 shadow-lg inline-block text-center"
            aria-label="Shop Equipment - Opens in new tab"
          >
            Shop Equipment
          </a>
        </div>
      </div>
    </section>
  );
}

import { Shield, Zap, ShoppingBag, Wrench } from 'lucide-react';

const equipmentCategories = [
  {
    icon: Shield,
    title: "Protective Gear",
    description: "Complete protection equipment including helmets, pads, and protective accessories for maximum safety on the ice.",
    items: ["Helmets & Cages", "Shoulder Pads", "Elbow Pads", "Gloves", "Shin Guards", "Protective Shorts"]
  },
  {
    icon: Zap,
    title: "Sticks & Equipment",
    description: "Professional-grade sticks and essential hockey equipment designed for performance and durability.",
    items: ["Hockey Sticks", "Goalie Sticks", "Tape & Accessories", "Bags", "Water Bottles", "Training Aids"]
  },
  {
    icon: ShoppingBag,
    title: "Apparel & Accessories",
    description: "Complete hockey apparel line including team uniforms, practice gear, and essential accessories.",
    items: ["Jerseys", "Practice Jerseys", "Socks", "Base Layers", "Warm-ups", "Casual Wear"]
  },
  {
    icon: Wrench,
    title: "Goals & Specialized",
    description: "Specialized equipment for goalies and training, plus goals and nets for practice and games.",
    items: ["Goalie Equipment", "Training Goals", "Shooting Aids", "Ice Hockey Goals", "Replacement Nets", "Goal Hardware"]
  }
];

export default function Equipment() {
  return (
    <section id="equipment" className="py-20 bg-slate-900">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Equipment We Offer</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            At The Ice Box we carry the biggest brands for players and goaltenders at every 
            level. From youth to professional, we have the equipment to take your game to the next level.
          </p>
        </div>

        <div className="grid md:grid-cols-2 gap-8">
          {equipmentCategories.map((category, index) => (
            <div key={index} className="bg-slate-800 p-8 rounded-xl shadow-lg border border-slate-700/50 hover:border-blue-500/50 transition-all duration-200">
              <div className="flex items-start space-x-4">
                <div className="bg-blue-600 w-12 h-12 rounded-lg flex items-center justify-center flex-shrink-0">
                  <category.icon className="w-6 h-6 text-white" />
                </div>
                <div className="flex-1">
                  <h3 className="text-xl font-bold text-white mb-3">{category.title}</h3>
                  <p className="text-gray-300 mb-4">{category.description}</p>
                  <div className="grid grid-cols-2 gap-2">
                    {category.items.map((item, itemIndex) => (
                      <div key={itemIndex} className="text-sm text-gray-400 hover:text-blue-400 transition-colors cursor-pointer">
                        • {item}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

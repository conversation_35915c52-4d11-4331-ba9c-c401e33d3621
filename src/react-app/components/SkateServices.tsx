import { Zap, <PERSON>ting<PERSON>, Wrench, <PERSON>fresh<PERSON><PERSON>, Hammer, Thermometer } from 'lucide-react';

const skateServices = [
  {
    icon: Zap,
    title: "Skate Sharpening",
    description: "Professional sharpening services using state-of-the-art equipment for optimal performance.",
    details: ["Standard Sharpening", "Custom Hollow Options", "Edge Balancing", "Quick Turnaround"]
  },
  {
    icon: Settings,
    title: "Blade Profiling",
    description: "Custom blade profiling to match your skating style and improve your on-ice performance.",
    details: ["Custom Profile Analysis", "Position-Specific Tuning", "Performance Optimization", "Professional Consultation"]
  },
  {
    icon: Wrench,
    title: "Blade & Holder Services",
    description: "Complete blade and holder maintenance including mounting, replacement, and adjustments.",
    details: ["Blade Mounting", "Holder Replacement", "Blade Alignment", "Runner Installation"]
  },
  {
    icon: RefreshCw,
    title: "Skate Repair",
    description: "Skate restoration and repair services to extend the life of your equipment.",
    details: ["Boot Reconditioning", "Eyelet Replacement", "Tongue Repair", "Blade Replacement"]
  },
  {
    icon: <PERSON>,
    title: "Holder Replacement",
    description: "Professional holder replacement and upgrade services for improved performance and durability.",
    details: ["Holder Upgrades", "Custom Mounting", "Compatibility Assessment", "Performance Testing"]
  },
  {
    icon: Thermometer,
    title: "Heat Molding",
    description: "Custom heat molding services for the perfect fit and maximum comfort in your skates.",
    details: ["Custom Fit Analysis", "Professional Heat Molding", "Comfort Optimization", "Performance Enhancement"]
  }
];

export default function SkateServices() {
  return (
    <section className="py-20 bg-slate-800">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="text-center mb-16">
          <h2 className="text-4xl font-bold text-white mb-4">Skate Services</h2>
          <p className="text-xl text-gray-300 max-w-3xl mx-auto">
            Our certified skate technicians will take care of all your skate sharpening and maintenance 
            needs to ensure optimal performance and safety.
          </p>
        </div>

        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {skateServices.map((service, index) => (
            <div key={index} className="bg-slate-900 p-6 rounded-xl shadow-lg border border-slate-700/50 hover:border-blue-500/50 transition-all duration-200 transform hover:scale-105">
              <div className="bg-blue-600 w-12 h-12 rounded-lg flex items-center justify-center mb-4">
                <service.icon className="w-6 h-6 text-white" />
              </div>
              <h3 className="text-xl font-bold text-white mb-3">{service.title}</h3>
              <p className="text-gray-300 mb-4 text-sm leading-relaxed">{service.description}</p>
              <ul className="space-y-1">
                {service.details.map((detail, detailIndex) => (
                  <li key={detailIndex} className="text-xs text-gray-400">
                    • {detail}
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
}

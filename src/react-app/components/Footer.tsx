import { MapPin, Phone, Mail } from 'lucide-react';
import Logo from './Logo';

export default function Footer() {
  return (
    <footer className="bg-slate-900 border-t border-slate-700">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid md:grid-cols-3 gap-8">
          {/* Company Info */}
          <div>
            <div className="mb-4">
              <Logo size="extra-large" clickable={true} />
            </div>
            <p className="text-gray-300 mb-6">
              Your premier destination for professional hockey equipment, expert fitting services, 
              and comprehensive solutions for players at every level.
            </p>
            <div className="text-sm text-gray-400">
              © 2024 The Ice Box. All rights reserved.
            </div>
          </div>

          {/* Contact Info */}
          <div>
            <h3 className="text-xl font-bold text-white mb-4">Contact Us</h3>
            <div className="space-y-3">
              <div className="flex items-center space-x-3 text-gray-300">
                <MapPin size={16} className="text-blue-400 flex-shrink-0" />
                <span className="text-sm">23770 S Western Ave, Harbor City, CA 90710</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <Phone size={16} className="text-blue-400 flex-shrink-0" />
                <span className="text-sm">(*************</span>
              </div>
              <div className="flex items-center space-x-3 text-gray-300">
                <Mail size={16} className="text-blue-400 flex-shrink-0" />
                <span className="text-sm"><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Store Hours */}
          <div>
            <h3 className="text-xl font-bold text-white mb-4">Store Hours</h3>
            <div className="space-y-2 text-sm text-gray-300">
              <div className="flex justify-between">
                <span>Monday - Friday:</span>
                <span>10:00 AM - 8:00 PM</span>
              </div>
              <div className="flex justify-between">
                <span>Saturday:</span>
                <span>9:00 AM - 6:00 PM</span>
              </div>
              <div className="flex justify-between">
                <span>Sunday:</span>
                <span>11:00 AM - 5:00 PM</span>
              </div>
            </div>
            <div className="mt-6">
              <a 
                href="https://store.iceboxhockey.com/" 
                target="_blank" 
                rel="noopener noreferrer"
                className="bg-blue-600 hover:bg-blue-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors inline-block text-center"
                aria-label="Visit Our Store - Opens in new tab"
              >
                Visit Our Store
              </a>
            </div>
          </div>
        </div>

        <div className="border-t border-slate-700 mt-8 pt-8 flex flex-col md:flex-row justify-between items-center">
          <div className="text-sm text-gray-400 mb-4 md:mb-0">
            Privacy Policy • Terms of Service • Sitemap
          </div>
          <div className="text-sm text-gray-400">
            Powered by The Ice Box Team
          </div>
        </div>
      </div>
    </footer>
  );
}

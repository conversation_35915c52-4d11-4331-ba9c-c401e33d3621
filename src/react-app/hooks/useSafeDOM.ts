import { useCallback, useRef } from 'react';

/**
 * Custom hook for safe DOM operations that prevents NotFoundError
 * by ensuring elements exist and are properly connected before manipulation
 */
export function useSafeDOM() {
  const operationRef = useRef<number>(0);

  /**
   * Safely remove a child element from its parent
   * Prevents "NotFoundError: Failed to execute 'removeChild' on 'Node'"
   */
  const safeRemoveChild = useCallback((element: Element | null): boolean => {
    if (!element) {
      console.warn('useSafeDOM: Cannot remove null element');
      return false;
    }

    try {
      // Check if element has a parent and is connected to the DOM
      if (!element.parentNode) {
        console.warn('useSafeDOM: Element has no parent node');
        return false;
      }

      // Check if element is still connected to the document
      if (!element.isConnected) {
        console.warn('useSafeDOM: Element is not connected to DOM');
        return false;
      }

      // Verify the element is actually a child of its parent
      if (!element.parentNode.contains(element)) {
        console.warn('useSafeDOM: Element is not a child of its parent');
        return false;
      }

      // Perform the safe removal
      element.parentNode.removeChild(element);
      return true;
    } catch (error) {
      console.error('useSafeDOM: Error removing element:', error);
      return false;
    }
  }, []);

  /**
   * Safely add event listener with automatic cleanup tracking
   */
  const safeAddEventListener = useCallback((
    element: Element | null,
    event: string,
    handler: EventListener,
    options?: boolean | AddEventListenerOptions
  ): (() => void) | null => {
    if (!element || !element.addEventListener) {
      console.warn('useSafeDOM: Cannot add event listener to invalid element');
      return null;
    }

    try {
      element.addEventListener(event, handler, options);
      
      // Return cleanup function
      return () => {
        if (element && element.removeEventListener && element.isConnected) {
          try {
            element.removeEventListener(event, handler, options);
          } catch (error) {
            console.warn('useSafeDOM: Error removing event listener:', error);
          }
        }
      };
    } catch (error) {
      console.error('useSafeDOM: Error adding event listener:', error);
      return null;
    }
  }, []);

  /**
   * Safely set attributes on an element
   */
  const safeSetAttribute = useCallback((
    element: Element | null,
    name: string,
    value: string
  ): boolean => {
    if (!element || !element.setAttribute) {
      console.warn('useSafeDOM: Cannot set attribute on invalid element');
      return false;
    }

    try {
      if (!element.isConnected) {
        console.warn('useSafeDOM: Element is not connected to DOM');
        return false;
      }

      element.setAttribute(name, value);
      return true;
    } catch (error) {
      console.error('useSafeDOM: Error setting attribute:', error);
      return false;
    }
  }, []);

  /**
   * Safely append child to parent element
   */
  const safeAppendChild = useCallback((
    parent: Element | null,
    child: Element | null
  ): boolean => {
    if (!parent || !child) {
      console.warn('useSafeDOM: Cannot append - parent or child is null');
      return false;
    }

    try {
      if (!parent.isConnected) {
        console.warn('useSafeDOM: Parent element is not connected to DOM');
        return false;
      }

      if (child.parentNode) {
        console.warn('useSafeDOM: Child element already has a parent');
        return false;
      }

      parent.appendChild(child);
      return true;
    } catch (error) {
      console.error('useSafeDOM: Error appending child:', error);
      return false;
    }
  }, []);

  /**
   * Generate unique operation ID for tracking
   */
  const getOperationId = useCallback((): string => {
    operationRef.current += 1;
    return `dom-op-${operationRef.current}-${Date.now()}`;
  }, []);

  return {
    safeRemoveChild,
    safeAddEventListener,
    safeSetAttribute,
    safeAppendChild,
    getOperationId
  };
}
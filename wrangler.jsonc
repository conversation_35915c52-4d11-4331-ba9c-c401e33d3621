{
  "$schema": "node_modules/wrangler/config-schema.json",
  "name": "0198c880-4ae9-7f32-af6f-cf7ab59f52dd",
  "main": "./src/worker/index.ts",
  "compatibility_date": "2025-06-17",
  "compatibility_flags": ["nodejs_compat"],
  "observability": {
    "enabled": true,
  },
  "upload_source_maps": true,
  "assets": {
    "not_found_handling": "single-page-application",
  },
  "d1_databases": [
    {
      "binding": "DB",
      "database_name": "0198c880-4ae9-7f32-af6f-cf7ab59f52dd",
      "database_id": "0198c880-4ae9-7f32-af6f-cf7ab59f52dd",
    },
  ],
}
